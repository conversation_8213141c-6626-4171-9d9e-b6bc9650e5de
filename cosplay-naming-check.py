#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "click",
# ]
# ///

import os
import re
from collections import defaultdict

import click
from pathlib import Path


def validate_filename(filename: str) -> bool:
    """Validate if filename follows the cosplay naming convention."""
    
    # Remove file extension
    name_without_ext = filename.rsplit('.', 1)[0]
    
    # First check: no multiple number sequences (like "02 2" or "04 07")
    if re.search(r'\s\d+\s+\d+$', name_without_ext):
            return False
    
    patterns = [
        # Single cosplayer with character: "Cosplayer - [Character]" or "Cosplayer - [Character] 01"
        r'^.+\s-\s\[[^\[\]]+\](\s\d+)?$',
        
        # Multiple cosplayers with characters: "Cosplayer1, Cosplayer2 - [Character1, Character2]" or with number
        r'^.+,.+\s-\s\[[^\[\]]+\](\s\d+)?$',
        
        # Multiple cosplayers without character: "Cosplayer1, Cosplayer2" or "Cosplayer1, Cosplayer2 01"
        r'^.+,.+(\s\d+)?$',
        
        # Single cosplayer without character: "Cosplayer" or "Cosplayer 01" (must be last to avoid conflicts)
        r'^[^,\[\]]+(\s\d+)?$',
    ]
    
    return any(re.match(pattern, name_without_ext) for pattern in patterns)


def check_numbering(filenames: list[str]) -> dict[str, list[int]]:
    """Check for missing numbers in filename sequences."""
    groups = defaultdict(list)
    base_name_map = {}  # To store the original capitalization of the base name

    # Regex to capture base name and number
    for filename in filenames:
        name_without_ext = filename.rsplit('.', 1)[0]

        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))

            lower_base_name = base_name.lower()

            # Debug: Print ALL files that get added to sakuracyan ägir group
            if lower_base_name == "sakuracyan - [ägir]":
                print(f"DEBUG: Adding to sakuracyan ägir group: {filename} -> number={number}")

            groups[lower_base_name].append(number)

            # Debug: Print sakuracyan matches
            if "sakuracyan" in filename and "ägir" in lower_base_name:
                print(f"DEBUG: Matched sakuracyan file: {filename} -> base='{base_name}', lower='{lower_base_name}', number={number}")
                if number >= 21:
                    print(f"DEBUG: HIGH NUMBER FILE: {filename}")

            # Store the first encountered capitalization, handling Unicode characters
            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name

    missing_by_group = {}
    for lower_base, numbers in groups.items():
        if len(numbers) <= 1:
            continue

        numbers.sort()

        # Debug: Print sakuracyan group info
        if "sakuracyan" in lower_base and "ägir" in lower_base:
            print(f"DEBUG: Processing sakuracyan group '{lower_base}' with numbers: {numbers}")
            print(f"DEBUG: Maps to original_base: '{base_name_map[lower_base]}'")

        missing = []

        # Check for missing numbers before the start of the sequence.
        # "01 is optional, sometimes it begins with 02".
        # So if '1' is not present, sequence must start with '2'.
        if "sakuracyan" in lower_base and "ägir" in lower_base:
            print(f"DEBUG: Checking start condition: 1 in numbers = {1 in numbers}, numbers[0] = {numbers[0]}")
            print(f"DEBUG: Condition result: {1 not in numbers and numbers[0] > 2}")

        if 1 not in numbers and numbers[0] > 2:
            before_missing = list(range(2, numbers[0]))
            missing.extend(before_missing)
            if "sakuracyan" in lower_base and "ägir" in lower_base:
                print(f"DEBUG: Missing before sequence: {before_missing}")

        # Check for gaps within the sequence
        if "sakuracyan" in lower_base and "ägir" in lower_base:
            print("DEBUG: Checking gaps in sequence...")
        for i in range(len(numbers) - 1):
            current = numbers[i]
            next_num = numbers[i+1]
            gap_condition = next_num - current > 1
            if "sakuracyan" in lower_base and "ägir" in lower_base:
                print(f"DEBUG:   Gap check {i}: {current} -> {next_num}, diff = {next_num - current}, condition = {gap_condition}")
            if gap_condition:
                gap_missing = list(range(current + 1, next_num))
                missing.extend(gap_missing)
                if "sakuracyan" in lower_base and "ägir" in lower_base:
                    print(f"DEBUG: Gap found: {gap_missing}")

        if "sakuracyan" in lower_base and "ägir" in lower_base:
            print(f"DEBUG: Final missing list before check: {missing}")
            print(f"DEBUG: missing is truthy: {bool(missing)}")

        if missing:
            original_base = base_name_map[lower_base]

            # Debug: Check for overwriting
            if original_base in missing_by_group:
                print(f"DEBUG: OVERWRITING! '{original_base}' already exists with {missing_by_group[original_base]}, replacing with {sorted(missing)}")

            # Debug: Check if this affects sakuracyan
            if "sakuracyan" in original_base:
                print(f"DEBUG: ADDING MISSING for sakuracyan! lower_base='{lower_base}', original_base='{original_base}', missing={sorted(missing)}")

            missing_by_group[original_base] = sorted(missing)
            if "sakuracyan" in lower_base and "ägir" in lower_base:
                print(f"DEBUG: Final missing for sakuracyan: {sorted(missing)}")
        elif "sakuracyan" in lower_base and "ägir" in lower_base:
            print("DEBUG: No missing numbers for sakuracyan")

    # Debug: Print all groups with ägir
    print("DEBUG: All groups with 'ägir':")
    for lower_base, numbers in groups.items():
        if "ägir" in lower_base:
            original_base = base_name_map.get(lower_base, lower_base)
            missing = missing_by_group.get(original_base, [])
            print(f"  '{lower_base}' -> '{original_base}': {sorted(numbers)} (missing: {missing})")

    # Debug: Print base_name_map for ägir entries
    print("DEBUG: base_name_map for 'ägir' entries:")
    for lower_base, original_base in base_name_map.items():
        if "ägir" in lower_base:
            print(f"  '{lower_base}' -> '{original_base}'")

    # Debug: Print final missing_by_group before return
    print("DEBUG: Final missing_by_group before return:")
    for key, value in missing_by_group.items():
        if "sakuracyan" in key or "yummychiyo" in key:
            print(f"  '{key}': {value}")

    return missing_by_group


@click.command()
@click.argument('folder_path', type=click.Path(exists=True, file_okay=False, dir_okay=True, path_type=Path))
@click.option('--verbose', '-v', is_flag=True, help='Show detailed validation results')
def main(folder_path: Path, verbose: bool):
    """Validate cosplay picture filenames in a folder."""
    
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.mp4'}
    
    valid_files = []
    invalid_files = []
    
    for file_path in folder_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in media_extensions:
            filename = file_path.name
            
            if validate_filename(filename):
                valid_files.append(filename)
                if verbose:
                    click.secho(f"✓ {filename}", fg='green')
            else:
                invalid_files.append(filename)
                if verbose:
                    click.secho(f"✗ {filename}", fg='red')
    
    total_files = len(valid_files) + len(invalid_files)
    
    if total_files == 0:
        click.echo("No media files found in the specified folder.")
        return
    
    missing_numbers = check_numbering(valid_files)

    # Debug: Print info about sakuracyan files
    sakura_files = [f for f in valid_files if "sakuracyan" in f and "Ägir" in f]
    if sakura_files:
        print(f"\nDEBUG: Found {len(sakura_files)} sakuracyan Ägir files")
        for f in sorted(sakura_files):
            print(f"  {f}")
        print(f"DEBUG: Missing numbers result: {missing_numbers.get('sakuracyan - [Ägir]', 'None')}")

    # Debug: Print all missing_numbers entries
    print(f"\nDEBUG: All missing_numbers entries ({len(missing_numbers)}):")
    for base, missing in missing_numbers.items():
        print(f"  '{base}': {missing}")
    
    click.echo("\nValidation Results:")
    click.echo(f"Total media files: {total_files}")
    click.echo(f"Valid filenames: {len(valid_files)} ({len(valid_files)/total_files*100:.1f}%)")
    click.echo(f"Invalid filenames: {len(invalid_files)} ({len(invalid_files)/total_files*100:.1f}%)")
    
    if invalid_files and not verbose:
        click.echo("\nInvalid files:")
        for filename in invalid_files:
            click.echo(f"  {filename}")
    
    if missing_numbers:
        click.echo("\nMissing file numbers:")
        for base, missing in missing_numbers.items():
            click.echo(f'  {base}: missing {", ".join(map(str, missing))}')

    if len(invalid_files) == 0 and not missing_numbers:
        click.echo("\n🎉 All files follow the naming convention and numbering is correct!")
    else:
        if len(invalid_files) > 0:
            click.echo(f"\n⚠️  {len(invalid_files)} files need to be renamed.")
        if missing_numbers:
            total_missing = sum(len(m) for m in missing_numbers.values())
            click.echo(f"\n⚠️  {total_missing} file number(s) are missing.")


if __name__ == '__main__':
    main()
