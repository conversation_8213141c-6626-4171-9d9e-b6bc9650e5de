#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "click",
# ]
# ///

import os
import re
from collections import defaultdict

import click
from pathlib import Path


def validate_filename(filename: str) -> bool:
    """Validate if filename follows the cosplay naming convention."""
    
    # Remove file extension
    name_without_ext = filename.rsplit('.', 1)[0]
    
    # First check: no multiple number sequences (like "02 2" or "04 07")
    if re.search(r'\s\d+\s+\d+$', name_without_ext):
            return False
    
    patterns = [
        # Single cosplayer with character: "Cosplayer - [Character]" or "Cosplayer - [Character] 01"
        r'^.+\s-\s\[[^\[\]]+\](\s\d+)?$',
        
        # Multiple cosplayers with characters: "Cosplayer1, Cosplayer2 - [Character1, Character2]" or with number
        r'^.+,.+\s-\s\[[^\[\]]+\](\s\d+)?$',
        
        # Multiple cosplayers without character: "Cosplayer1, Cosplayer2" or "Cosplayer1, Cosplayer2 01"
        r'^.+,.+(\s\d+)?$',
        
        # Single cosplayer without character: "Cosplayer" or "Cosplayer 01" (must be last to avoid conflicts)
        r'^[^,\[\]]+(\s\d+)?$',
    ]
    
    return any(re.match(pattern, name_without_ext) for pattern in patterns)


def check_numbering(filenames: list[str]) -> dict[str, list[int]]:
    """Check for missing numbers in filename sequences."""
    groups = defaultdict(list)
    base_name_map = {}  # To store the original capitalization of the base name

    # Regex to capture base name and number
    for filename in filenames:
        name_without_ext = filename.rsplit('.', 1)[0]

        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))
            
            lower_base_name = base_name.lower()
            groups[lower_base_name].append(number)
            
            # Store the first encountered capitalization, handling Unicode characters
            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name

    missing_by_group = {}
    for lower_base, numbers in groups.items():
        if len(numbers) <= 1:
            continue

        numbers.sort()

        missing = []

        # Check for missing numbers before the start of the sequence.
        # "01 is optional, sometimes it begins with 02".
        # So if '1' is not present, sequence must start with '2'.
        if 1 not in numbers and numbers[0] > 2:
            missing.extend(range(2, numbers[0]))

        # Check for gaps within the sequence
        for i in range(len(numbers) - 1):
            if numbers[i+1] - numbers[i] > 1:
                missing.extend(range(numbers[i] + 1, numbers[i+1]))

        if missing:
            original_base = base_name_map[lower_base]
            missing_by_group[original_base] = sorted(missing)

    return missing_by_group


@click.command()
@click.argument('folder_path', type=click.Path(exists=True, file_okay=False, dir_okay=True, path_type=Path))
@click.option('--verbose', '-v', is_flag=True, help='Show detailed validation results')
def main(folder_path: Path, verbose: bool):
    """Validate cosplay picture filenames in a folder."""
    
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.mp4'}
    
    valid_files = []
    invalid_files = []
    
    for file_path in folder_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in media_extensions:
            filename = file_path.name
            
            if validate_filename(filename):
                valid_files.append(filename)
                if verbose:
                    click.echo(f"✓ {filename}", fg='green')
            else:
                invalid_files.append(filename)
                if verbose:
                    click.echo(f"✗ {filename}", fg='red')
    
    total_files = len(valid_files) + len(invalid_files)
    
    if total_files == 0:
        click.echo("No media files found in the specified folder.")
        return
    
    missing_numbers = check_numbering(valid_files)
    
    click.echo("\nValidation Results:")
    click.echo(f"Total media files: {total_files}")
    click.echo(f"Valid filenames: {len(valid_files)} ({len(valid_files)/total_files*100:.1f}%)")
    click.echo(f"Invalid filenames: {len(invalid_files)} ({len(invalid_files)/total_files*100:.1f}%)")
    
    if invalid_files and not verbose:
        click.echo("\nInvalid files:")
        for filename in invalid_files:
            click.echo(f"  {filename}")
    
    if missing_numbers:
        click.echo("\nMissing file numbers:")
        for base, missing in missing_numbers.items():
            click.echo(f'  {base}: missing {", ".join(map(str, missing))}')

    if len(invalid_files) == 0 and not missing_numbers:
        click.echo("\n🎉 All files follow the naming convention and numbering is correct!")
    else:
        if len(invalid_files) > 0:
            click.echo(f"\n⚠️  {len(invalid_files)} files need to be renamed.")
        if missing_numbers:
            total_missing = sum(len(m) for m in missing_numbers.values())
            click.echo(f"\n⚠️  {total_missing} file number(s) are missing.")


if __name__ == '__main__':
    main()