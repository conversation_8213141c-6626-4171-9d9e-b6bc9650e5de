#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "click",
# ]
# ///

import os
import re
from collections import defaultdict

import click
from pathlib import Path


def validate_filename(filename: str) -> bool:
    """Validate if filename follows the cosplay naming convention."""
    
    # Remove file extension
    name_without_ext = filename.rsplit('.', 1)[0]
    
    # First check: no multiple number sequences (like "02 2" or "04 07")
    if re.search(r'\s\d+\s+\d+$', name_without_ext):
            return False
    
    patterns = [
        # Single cosplayer with character: "Cosplayer - [Character]" or "Cosplayer - [Character] 01"
        r'^.+\s-\s\[[^\[\]]+\](\s\d+)?$',
        
        # Multiple cosplayers with characters: "Cosplayer1, Cosplayer2 - [Character1, Character2]" or with number
        r'^.+,.+\s-\s\[[^\[\]]+\](\s\d+)?$',
        
        # Multiple cosplayers without character: "Cosplayer1, Cosplayer2" or "Cosplayer1, Cosplayer2 01"
        r'^.+,.+(\s\d+)?$',
        
        # Single cosplayer without character: "Cosplayer" or "Cosplayer 01" (must be last to avoid conflicts)
        r'^[^,\[\]]+(\s\d+)?$',
    ]
    
    return any(re.match(pattern, name_without_ext) for pattern in patterns)


def check_numbering(filenames: list[str]) -> dict[str, list[int]]:
    """Check for missing numbers in filename sequences."""
    groups = defaultdict(list)
    base_name_map = {}  # To store the original capitalization of the base name

    # Regex to capture base name and number
    for filename in filenames:
        name_without_ext = filename.rsplit('.', 1)[0]

        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))

            lower_base_name = base_name.lower()

            # Debug: Track all files that get added to sakuracyan ägir
            if lower_base_name == "sakuracyan - [ägir]":
                print(f"DEBUG: File '{filename}' -> base='{base_name}' -> lower='{lower_base_name}' -> number={number}")
                print(f"DEBUG: Group size before adding: {len(groups[lower_base_name])}")

            groups[lower_base_name].append(number)

            # Debug: Track group size after adding
            if lower_base_name == "sakuracyan - [ägir]":
                print(f"DEBUG: Group size after adding: {len(groups[lower_base_name])}")

            # Store the first encountered capitalization, handling Unicode characters
            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name

    # Debug: Check sakuracyan ägir group before processing
    sakura_key = "sakuracyan - [ägir]"
    if sakura_key in groups:
        print(f"DEBUG: sakuracyan ägir group has {len(groups[sakura_key])} numbers: {sorted(groups[sakura_key])}")
        # Check for duplicates
        numbers = groups[sakura_key]
        unique_numbers = set(numbers)
        if len(numbers) != len(unique_numbers):
            print(f"DEBUG: DUPLICATES FOUND! {len(numbers)} total, {len(unique_numbers)} unique")
            from collections import Counter
            counts = Counter(numbers)
            duplicates = {num: count for num, count in counts.items() if count > 1}
            print(f"DEBUG: Duplicate numbers: {duplicates}")

        # Check the actual numbers to see if there are two ranges
        if len(numbers) > 20:
            print(f"DEBUG: Large group detected! Numbers: {sorted(numbers)}")
            print(f"DEBUG: Min: {min(numbers)}, Max: {max(numbers)}")
            print(f"DEBUG: Range 1-20: {[n for n in numbers if 1 <= n <= 20]}")
            print(f"DEBUG: Range 21+: {[n for n in numbers if n > 20]}")

    # Debug: Check ALL groups that contain ägir
    print("DEBUG: ALL groups containing 'ägir':")
    agir_groups = []
    for lower_base, numbers in groups.items():
        if "ägir" in lower_base:
            agir_groups.append((lower_base, numbers))
            print(f"  '{lower_base}': {len(numbers)} numbers")

    print(f"DEBUG: Found {len(agir_groups)} groups containing 'ägir'")

    # Debug: Check for any groups that map to sakuracyan Ägir
    print("DEBUG: All groups that might affect sakuracyan:")
    sakuracyan_agir_groups = []
    for lower_base, numbers in groups.items():
        if lower_base in base_name_map:
            original_base = base_name_map[lower_base]
            if "sakuracyan" in original_base.lower() and "ägir" in original_base.lower():
                sakuracyan_agir_groups.append((lower_base, original_base, numbers))
                print(f"  '{lower_base}' -> '{original_base}': {len(numbers)} numbers: {sorted(numbers)}")

    print(f"DEBUG: Found {len(sakuracyan_agir_groups)} groups that map to sakuracyan Ägir")

    missing_by_group = {}

    # Convert defaultdict to regular dict to avoid any potential issues
    groups_dict = dict(groups)

    # Debug: Show all keys before iteration
    all_keys = list(groups_dict.keys())
    sakuracyan_keys = [k for k in all_keys if "sakuracyan" in k]
    print(f"DEBUG: Total groups: {len(all_keys)}")
    print(f"DEBUG: Sakuracyan groups: {len(sakuracyan_keys)}")

    # Check for duplicate-looking keys
    agir_keys = [k for k in sakuracyan_keys if "ägir" in k]
    print(f"DEBUG: Found {len(agir_keys)} sakuracyan ägir keys:")
    for i, k in enumerate(agir_keys):
        print(f"  Key {i}: '{k}': {len(groups_dict[k])} numbers")
        print(f"    Bytes: {k.encode('utf-8')}")
        print(f"    Repr: {repr(k)}")

    for k in sakuracyan_keys:
        if "ägir" not in k:
            print(f"  '{k}': {len(groups_dict[k])} numbers")

    iteration_count = 0
    for lower_base, numbers in groups_dict.items():
        iteration_count += 1

        if len(numbers) <= 1:
            continue

        # Debug: Show ALL groups being processed
        if "sakuracyan" in lower_base:
            print(f"DEBUG: Iteration {iteration_count}: Processing group '{lower_base}' with {len(numbers)} numbers")
            print(f"DEBUG: Current group size in dict: {len(groups_dict[lower_base])}")
            print(f"DEBUG: numbers is same object as groups_dict[lower_base]: {numbers is groups_dict[lower_base]}")

        # Debug: Show numbers before sorting
        if lower_base == "sakuracyan - [ägir]":
            print(f"DEBUG: Before sort - numbers: {numbers}")

        numbers.sort()

        # Debug: Show numbers after sorting
        if lower_base == "sakuracyan - [ägir]":
            print(f"DEBUG: After sort - numbers: {numbers}")

        missing = []

        # Debug for sakuracyan ägir
        if lower_base == "sakuracyan - [ägir]":
            print(f"DEBUG: Processing sakuracyan with numbers: {numbers}")
            print(f"DEBUG: 1 in numbers: {1 in numbers}")
            print(f"DEBUG: numbers[0]: {numbers[0]}")
            print(f"DEBUG: Condition (1 not in numbers and numbers[0] > 2): {1 not in numbers and numbers[0] > 2}")

        # Check for missing numbers before the start of the sequence.
        # "01 is optional, sometimes it begins with 02".
        # So if '1' is not present, sequence must start with '2'.
        if 1 not in numbers and numbers[0] > 2:
            before_missing = list(range(2, numbers[0]))
            missing.extend(before_missing)
            if lower_base == "sakuracyan - [ägir]":
                print(f"DEBUG: Added before missing: {before_missing}")

        # Check for gaps within the sequence
        for i in range(len(numbers) - 1):
            if numbers[i+1] - numbers[i] > 1:
                gap_missing = list(range(numbers[i] + 1, numbers[i+1]))
                missing.extend(gap_missing)
                if lower_base == "sakuracyan - [ägir]":
                    print(f"DEBUG: Gap found between {numbers[i]} and {numbers[i+1]}: {gap_missing}")

        if lower_base == "sakuracyan - [ägir]":
            print(f"DEBUG: Final missing for sakuracyan: {missing}")

        if missing:
            original_base = base_name_map[lower_base]

            # Debug: Check for overwriting
            if original_base in missing_by_group:
                print(f"DEBUG: OVERWRITING '{original_base}'! Old: {missing_by_group[original_base]}, New: {sorted(missing)}")

            missing_by_group[original_base] = sorted(missing)

            # Debug: Check if this affects sakuracyan
            if "sakuracyan" in original_base.lower():
                print(f"DEBUG: ADDING missing for '{original_base}' from group '{lower_base}': {sorted(missing)}")

    return missing_by_group


@click.command()
@click.argument('folder_path', type=click.Path(exists=True, file_okay=False, dir_okay=True, path_type=Path))
@click.option('--verbose', '-v', is_flag=True, help='Show detailed validation results')
def main(folder_path: Path, verbose: bool):
    """Validate cosplay picture filenames in a folder."""
    
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.mp4'}
    
    valid_files = []
    invalid_files = []
    
    for file_path in folder_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in media_extensions:
            filename = file_path.name
            
            if validate_filename(filename):
                valid_files.append(filename)
                if verbose:
                    click.secho(f"✓ {filename}", fg='green')
            else:
                invalid_files.append(filename)
                if verbose:
                    click.secho(f"✗ {filename}", fg='red')
    
    total_files = len(valid_files) + len(invalid_files)
    
    if total_files == 0:
        click.echo("No media files found in the specified folder.")
        return
    
    missing_numbers = check_numbering(valid_files)

    # Debug: Check what check_numbering returned
    print(f"DEBUG: check_numbering returned {len(missing_numbers)} entries")
    for key, value in missing_numbers.items():
        if "sakuracyan" in key or "yummychiyo" in key:
            print(f"DEBUG: Returned '{key}': {value}")
    
    click.echo("\nValidation Results:")
    click.echo(f"Total media files: {total_files}")
    click.echo(f"Valid filenames: {len(valid_files)} ({len(valid_files)/total_files*100:.1f}%)")
    click.echo(f"Invalid filenames: {len(invalid_files)} ({len(invalid_files)/total_files*100:.1f}%)")
    
    if invalid_files and not verbose:
        click.echo("\nInvalid files:")
        for filename in invalid_files:
            click.echo(f"  {filename}")
    
    if missing_numbers:
        click.echo("\nMissing file numbers:")
        for base, missing in missing_numbers.items():
            click.echo(f'  {base}: missing {", ".join(map(str, missing))}')

    if len(invalid_files) == 0 and not missing_numbers:
        click.echo("\n🎉 All files follow the naming convention and numbering is correct!")
    else:
        if len(invalid_files) > 0:
            click.echo(f"\n⚠️  {len(invalid_files)} files need to be renamed.")
        if missing_numbers:
            total_missing = sum(len(m) for m in missing_numbers.values())
            click.echo(f"\n⚠️  {total_missing} file number(s) are missing.")


if __name__ == '__main__':
    main()
