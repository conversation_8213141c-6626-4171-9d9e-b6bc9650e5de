#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "click",
# ]
# ///

import re
import unicodedata
from collections import defaultdict
from pathlib import Path
import click

def validate_filename(filename: str) -> bool:
    """Validate filename against cosplay naming patterns."""
    name_without_ext = filename.rsplit('.', 1)[0]
    
    patterns = [
        r'^[^-]+ - \[[^\]]+\]$',
        r'^[^-]+ - \[[^\]]+\] \d+$',
        r'^[^-]+$',
        r'^[^-]+ \d+$',
        r'^[^-]+, [^-]+ - \[[^\]]+\]$',
        r'^[^-]+, [^-]+ - \[[^\]]+\] \d+$',
        r'^[^-]+, [^-]+$',
        r'^[^-]+, [^-]+ \d+$',
    ]
    
    return any(re.match(pattern, name_without_ext) for pattern in patterns)

def check_numbering(folder_path: Path, verbose: bool = False) -> dict:
    """Check for missing file numbers in numbered sequences."""
    
    # Media file extensions
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.mp4'}
    
    # Group files by base name (without number)
    groups = defaultdict(list)
    base_name_map = {}  # Maps lowercase base name to original case
    
    total_files = 0
    valid_files = 0
    
    # Process all files
    sakuracyan_agir_files = []

    for file_path in folder_path.rglob('*'):
        if not file_path.is_file():
            continue

        if file_path.suffix.lower() not in media_extensions:
            continue

        total_files += 1
        filename = file_path.name

        # Normalize Unicode to handle different representations of the same character
        normalized_filename = unicodedata.normalize('NFC', filename)

        # Debug: Track sakuracyan Ägir files
        if "sakuracyan" in normalized_filename.lower() and "ägir" in normalized_filename.lower():
            sakuracyan_agir_files.append(normalized_filename)

        if not validate_filename(filename):
            if verbose:
                print(f"Invalid filename: {filename}")
            continue

        valid_files += 1
        
        # Extract base name and number
        name_without_ext = filename.rsplit('.', 1)[0]
        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        
        if match:
            base_name = match.group(1)
            number = int(match.group(2))
            
            # Normalize for grouping (lowercase)
            lower_base_name = base_name.lower()
            
            # Store original case mapping
            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name
            
            groups[lower_base_name].append(number)
            
            if verbose and "sakuracyan" in lower_base_name and "ägir" in lower_base_name:
                print(f"Added: {filename} -> base='{base_name}' -> number={number}")
    
    print(f"Total media files: {total_files}")
    print(f"Valid filenames: {valid_files} ({valid_files/total_files*100:.1f}%)")
    print(f"Invalid filenames: {total_files-valid_files} ({(total_files-valid_files)/total_files*100:.1f}%)")

    # Debug: Show sakuracyan Ägir files found
    if verbose:
        print(f"\nFound {len(sakuracyan_agir_files)} sakuracyan Ägir files:")
        for f in sorted(sakuracyan_agir_files):
            print(f"  {f}")
    
    # Check for missing numbers in each group
    missing_by_group = {}
    
    for lower_base, numbers in groups.items():
        if len(numbers) <= 1:
            continue
            
        # Sort numbers and find gaps
        sorted_numbers = sorted(set(numbers))  # Remove duplicates and sort
        
        if verbose and "sakuracyan" in lower_base and "ägir" in lower_base:
            print(f"Group '{lower_base}': {len(numbers)} files, numbers {sorted_numbers}")
        
        # Find missing numbers in the sequence
        if len(sorted_numbers) > 1:
            min_num = sorted_numbers[0]
            max_num = sorted_numbers[-1]
            expected = set(range(min_num, max_num + 1))
            actual = set(sorted_numbers)
            missing = sorted(expected - actual)
            
            if missing:
                original_base = base_name_map[lower_base]
                missing_by_group[original_base] = missing
                
                if verbose and "sakuracyan" in lower_base and "ägir" in lower_base:
                    print(f"Missing numbers for '{original_base}': {missing}")
    
    return missing_by_group

@click.command()
@click.argument('folder_path', type=click.Path(exists=True, path_type=Path))
@click.option('--verbose', '-v', is_flag=True, help='Show detailed output')
def main(folder_path: Path, verbose: bool):
    """Check cosplay file naming conventions and find missing numbers."""
    
    missing_by_group = check_numbering(folder_path, verbose)
    
    if missing_by_group:
        print("\nMissing file numbers:")
        total_missing = 0
        for group_name, missing_numbers in missing_by_group.items():
            missing_str = ', '.join(map(str, missing_numbers))
            print(f"  {group_name}: missing {missing_str}")
            total_missing += len(missing_numbers)
        
        print(f"\n⚠️  {total_missing} file number(s) are missing.")
    else:
        print("\n✅ No missing file numbers found.")

if __name__ == '__main__':
    main()
