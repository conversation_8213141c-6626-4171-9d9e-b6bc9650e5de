#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import re

def validate_filename(filename: str) -> bool:
    """Validate filename against cosplay naming patterns."""
    name_without_ext = filename.rsplit('.', 1)[0]
    
    patterns = [
        r'^[^-]+ - \[[^\]]+\]$',
        r'^[^-]+ - \[[^\]]+\] \d+$',
        r'^[^-]+$',
        r'^[^-]+ \d+$',
        r'^[^-]+, [^-]+ - \[[^\]]+\]$',
        r'^[^-]+, [^-]+ - \[[^\]]+\] \d+$',
        r'^[^-]+, [^-]+$',
        r'^[^-]+, [^-]+ \d+$',
    ]
    
    return any(re.match(pattern, name_without_ext) for pattern in patterns)

# Test all sakuracyan Ägir files
test_files = [
    "sakuracyan - [Ägir] 01.jpg",
    "sakuracyan - [Ägir] 20.jpg", 
    "sakuracyan - [Ägir] 21.jpg",
    "sakuracyan - [Ägir] 30.jpg",
    "sakuracyan - [Ägir] 50.jpg"
]

print("Testing filename validation:")
for filename in test_files:
    valid = validate_filename(filename)
    print(f"  {filename}: {'VALID' if valid else 'INVALID'}")

# Test the regex pattern that extracts numbers
print("\nTesting number extraction:")
for filename in test_files:
    name_without_ext = filename.rsplit('.', 1)[0]
    match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
    if match:
        base_name = match.group(1)
        number = int(match.group(2))
        print(f"  {filename}: base='{base_name}', number={number}")
    else:
        print(f"  {filename}: NO MATCH")
