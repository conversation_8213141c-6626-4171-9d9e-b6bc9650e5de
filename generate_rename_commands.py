#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import unicodedata
from pathlib import Path

def generate_rename_commands():
    """Generate shell commands to fix Unicode normalization issues."""
    
    folder_path = Path("~/Pictures/Anime/Cosplay").expanduser()
    
    # Find all sakuracyan Ägir files
    precomposed = []  # Files using Ä (U+00C4)
    decomposed = []   # Files using A + combining diaeresis (U+0041 + U+0308)
    
    for file_path in folder_path.rglob('*'):
        if not file_path.is_file():
            continue
            
        filename = file_path.name
        
        # Check if it's a sakuracyan Ägir file
        if "sakuracyan" in filename.lower():
            filename_bytes = filename.encode('utf-8')
            
            if b'\xc3\x84' in filename_bytes:  # Precomposed Ä
                if "gir" in filename:  # Make sure it's the Ägir character
                    precomposed.append(file_path)
            elif b'A\xcc\x88' in filename_bytes:  # A + combining diaeresis
                if "gir" in filename:  # Make sure it's the Ägir character
                    decomposed.append(file_path)
    
    print(f"Found {len(precomposed)} precomposed and {len(decomposed)} decomposed sakuracyan Ägir files")
    
    # Since there are more decomposed files (30) than precomposed (20),
    # it's easier to rename the precomposed files to decomposed
    
    print(f"\n# Commands to rename precomposed files to decomposed (recommended):")
    print(f"cd ~/Pictures/Anime/Cosplay")
    
    for file_path in sorted(precomposed):
        old_name = file_path.name
        # Create the decomposed version
        new_name = old_name.replace('\u00C4', 'A\u0308')  # Replace Ä with A + combining diaeresis
        
        # Escape special characters for shell
        old_escaped = old_name.replace('[', r'\[').replace(']', r'\]')
        new_escaped = new_name.replace('[', r'\[').replace(']', r'\]')
        
        print(f"mv '{old_escaped}' '{new_escaped}'")
    
    print(f"\n# Alternative: Commands to rename decomposed files to precomposed:")
    print(f"cd ~/Pictures/Anime/Cosplay")
    
    for file_path in sorted(decomposed):
        old_name = file_path.name
        # Create the precomposed version
        new_name = old_name.replace('A\u0308', '\u00C4')  # Replace A + combining diaeresis with Ä
        
        # Escape special characters for shell
        old_escaped = old_name.replace('[', r'\[').replace(']', r'\]')
        new_escaped = new_name.replace('[', r'\[').replace(']', r'\]')
        
        print(f"mv '{old_escaped}' '{new_escaped}'")

if __name__ == '__main__':
    generate_rename_commands()
