#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import unicodedata
from pathlib import Path

def analyze_unicode_filenames(folder_path: Path):
    """Find files with Unicode normalization issues."""
    
    # Find all sakuracyan Ägir files
    sakuracyan_files = []
    
    for file_path in folder_path.rglob('*'):
        if not file_path.is_file():
            continue
            
        filename = file_path.name
        
        # Check if it's a sakuracyan Ägir file (using both possible Unicode forms)
        if "sakuracyan" in filename.lower() and ("ägir" in filename.lower() or "a\u0308gir" in filename.lower()):
            sakuracyan_files.append(file_path)
    
    print(f"Found {len(sakuracyan_files)} sakuracyan Ägir files")
    
    # Group by Unicode normalization form
    precomposed = []  # Files using Ä (U+00C4)
    decomposed = []   # Files using A + combining diaeresis (U+0041 + U+0308)
    
    for file_path in sorted(sakuracyan_files):
        filename = file_path.name
        
        # Check the raw bytes of the filename
        filename_bytes = filename.encode('utf-8')
        
        if b'\xc3\x84' in filename_bytes:  # Precomposed Ä
            precomposed.append(file_path)
        elif b'A\xcc\x88' in filename_bytes:  # A + combining diaeresis
            decomposed.append(file_path)
        else:
            print(f"Unknown encoding: {filename} -> {filename_bytes}")
    
    print(f"\nPrecomposed (Ä): {len(precomposed)} files")
    for f in precomposed[:5]:  # Show first 5
        print(f"  {f.name}")
    if len(precomposed) > 5:
        print(f"  ... and {len(precomposed) - 5} more")
    
    print(f"\nDecomposed (A + ◌̈): {len(decomposed)} files")
    for f in decomposed[:5]:  # Show first 5
        print(f"  {f.name}")
    if len(decomposed) > 5:
        print(f"  ... and {len(decomposed) - 5} more")
    
    # Recommend which files to rename
    if len(precomposed) > 0 and len(decomposed) > 0:
        print("\n🔧 RECOMMENDATION:")
        print(f"Since you have more decomposed files ({len(decomposed)}) than precomposed ({len(precomposed)}),")
        print(f"rename the {len(precomposed)} precomposed files to use decomposed form.")
        print("\nFiles to rename (from precomposed Ä to decomposed A + ◌̈):")

        for f in precomposed:
            old_name = f.name
            # Convert precomposed Ä to decomposed A + combining diaeresis
            new_name = unicodedata.normalize('NFD', old_name)
            print(f"  mv '{old_name}' '{new_name}'")

        print("\nAlternatively, you could rename the decomposed files to precomposed:")
        print("Files to rename (from decomposed A + ◌̈ to precomposed Ä):")
        for f in decomposed[:5]:  # Show first 5 as example
            old_name = f.name
            # Convert decomposed to precomposed
            new_name = unicodedata.normalize('NFC', old_name)
            print(f"  mv '{old_name}' '{new_name}'")
        if len(decomposed) > 5:
            print(f"  ... and {len(decomposed) - 5} more similar renames")
    
    return precomposed, decomposed

if __name__ == '__main__':
    folder_path = Path("~/Pictures/Anime/Cosplay").expanduser()
    analyze_unicode_filenames(folder_path)
