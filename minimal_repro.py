#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import os
import re
from collections import defaultdict

def validate_filename(filename: str) -> bool:
    """Validate filename against cosplay naming patterns (copied from original)."""
    name_without_ext = filename.rsplit('.', 1)[0]

    patterns = [
        r'^[^-]+ - \[[^\]]+\]$',
        r'^[^-]+ - \[[^\]]+\] \d+$',
        r'^[^-]+$',
        r'^[^-]+ \d+$',
        r'^[^-]+, [^-]+ - \[[^\]]+\]$',
        r'^[^-]+, [^-]+ - \[[^\]]+\] \d+$',
        r'^[^-]+, [^-]+$',
        r'^[^-]+, [^-]+ \d+$',
    ]

    return any(re.match(pattern, name_without_ext) for pattern in patterns)

def minimal_repro(folder_path):
    """Minimal reproduction of the bug using same logic as original."""

    # Filter files same as original
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.mp4'}

    valid_files = []
    sakuracyan_files = []

    for filename in os.listdir(folder_path):
        if not any(filename.lower().endswith(ext) for ext in media_extensions):
            continue

        if validate_filename(filename):
            valid_files.append(filename)
            if "sakuracyan" in filename.lower() and "ägir" in filename.lower():
                sakuracyan_files.append(filename)
        else:
            if "sakuracyan" in filename.lower() and "ägir" in filename.lower():
                print(f"INVALID sakuracyan file: {filename}")

    print(f"Valid sakuracyan ägir files: {len(sakuracyan_files)}")
    for f in sorted(sakuracyan_files):
        print(f"  {f}")

    groups = defaultdict(list)
    base_name_map = {}

    # Process only valid files (same as original)
    for filename in valid_files:
        name_without_ext = filename.rsplit('.', 1)[0]
        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))
            lower_base_name = base_name.lower()

            groups[lower_base_name].append(number)

            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name
    
    # Check the sakuracyan group
    sakura_key = "sakuracyan - [ägir]"
    if sakura_key in groups:
        print(f"Sakuracyan group: {len(groups[sakura_key])} numbers: {sorted(groups[sakura_key])}")
    
    # Process missing numbers (same logic as original)
    missing_by_group = {}
    
    for lower_base, numbers in groups.items():
        if len(numbers) <= 1:
            continue
        
        numbers.sort()
        
        missing = []
        
        # Same logic as original
        if 1 not in numbers and numbers[0] > 2:
            missing.extend(range(2, numbers[0]))
        
        for i in range(len(numbers) - 1):
            if numbers[i+1] - numbers[i] > 1:
                missing.extend(range(numbers[i] + 1, numbers[i+1]))
        
        if missing:
            original_base = base_name_map[lower_base]
            
            # Debug only sakuracyan
            if lower_base == sakura_key:
                print(f"SAKURACYAN: Processing '{lower_base}' with numbers {numbers}")
                print(f"SAKURACYAN: Found missing: {missing}")
                print(f"SAKURACYAN: Adding to missing_by_group['{original_base}']")
            
            missing_by_group[original_base] = sorted(missing)
    
    # Check final result
    target_original = 'sakuracyan - [Ägir]'
    if target_original in missing_by_group:
        print(f"FINAL RESULT: '{target_original}': {missing_by_group[target_original]}")
    else:
        print(f"FINAL RESULT: '{target_original}' not in missing_by_group (correct!)")
    
    return missing_by_group

if __name__ == "__main__":
    folder_path = "/Users/<USER>/Pictures/Anime/Cosplay"
    result = minimal_repro(folder_path)
    
    # Show only sakuracyan result
    target = 'sakuracyan - [Ägir]'
    if target in result:
        print(f"\nBUG REPRODUCED: {target} has missing numbers: {result[target]}")
    else:
        print(f"\nBUG NOT REPRODUCED: {target} correctly has no missing numbers")
