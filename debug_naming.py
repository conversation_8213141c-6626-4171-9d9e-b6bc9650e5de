#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import os
import re
from collections import defaultdict
from pathlib import Path

def debug_check_numbering(filenames: list[str]) -> dict[str, list[int]]:
    """Debug version of check_numbering to see what's happening."""
    groups = defaultdict(list)
    base_name_map = {}
    
    print("=== DEBUG: Processing filenames ===")
    
    # Regex to capture base name and number
    for filename in filenames:
        name_without_ext = filename.rsplit('.', 1)[0]
        print(f"Processing: {filename} -> {name_without_ext}")

        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))
            
            print(f"  Matched: base='{base_name}', number={number}")
            
            lower_base_name = base_name.lower()
            groups[lower_base_name].append(number)
            
            # Store the first encountered capitalization
            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name
                print(f"  Stored base_name_map['{lower_base_name}'] = '{base_name}'")
        else:
            print(f"  No match for: {name_without_ext}")

    print("\n=== DEBUG: Groups found ===")
    for lower_base, numbers in groups.items():
        original_base = base_name_map[lower_base]
        print(f"'{lower_base}' -> '{original_base}': {sorted(numbers)}")

    missing_by_group = {}
    for lower_base, numbers in groups.items():
        if len(numbers) <= 1:
            print(f"Skipping '{lower_base}' - only {len(numbers)} file(s)")
            continue

        numbers.sort()
        print(f"\nChecking '{lower_base}' with numbers: {numbers}")

        missing = []

        # Check for missing numbers before the start of the sequence
        print(f"  Checking start: 1 in numbers = {1 in numbers}, numbers[0] = {numbers[0]}")
        if 1 not in numbers and numbers[0] > 2:
            before_missing = list(range(2, numbers[0]))
            missing.extend(before_missing)
            print(f"  Missing before sequence: {before_missing}")
        else:
            print("  No missing before sequence")

        # Check for gaps within the sequence
        print("  Checking gaps in sequence...")
        for i in range(len(numbers) - 1):
            current = numbers[i]
            next_num = numbers[i+1]
            print(f"    Checking gap between {current} and {next_num}: diff = {next_num - current}")
            if next_num - current > 1:
                gap = list(range(current + 1, next_num))
                missing.extend(gap)
                print(f"    Gap found: {gap}")
        print("  Gap check complete")

        if missing:
            original_base = base_name_map[lower_base]
            missing_by_group[original_base] = sorted(missing)
            print(f"  Final missing for '{original_base}': {sorted(missing)}")
        else:
            print(f"  No missing numbers for '{lower_base}'")

    return missing_by_group

# Test with ALL files to see what happens
test_folder = Path("~/Pictures/Anime/Cosplay").expanduser()
media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.mp4'}

all_filenames = []
sakura_filenames = []
for file_path in test_folder.iterdir():
    if file_path.is_file() and file_path.suffix.lower() in media_extensions:
        filename = file_path.name
        all_filenames.append(filename)
        if "sakuracyan" in filename and "Ägir" in filename:
            sakura_filenames.append(filename)

print(f"Found {len(all_filenames)} total files")
print(f"Found {len(sakura_filenames)} sakuracyan Ägir files")

print("\n" + "="*50)
print("Testing with ALL files:")
result_all = debug_check_numbering(all_filenames)
print(f"\nResult for all files: {result_all}")

print("\n" + "="*50)
print("Testing with just sakuracyan files:")
result_sakura = debug_check_numbering(sakura_filenames)
print(f"\nResult for sakuracyan files: {result_sakura}")
