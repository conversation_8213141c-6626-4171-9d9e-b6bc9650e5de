#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import os
import re
from collections import defaultdict
from pathlib import Path

def debug_sakuracyan_agir(folder_path):
    """Debug the sakuracyan Ägir issue specifically."""

    # Find ALL files and process them to see what maps to sakuracyan Ägir
    all_files = []
    for filename in os.listdir(folder_path):
        if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
            all_files.append(filename)

    print(f"Processing {len(all_files)} total files to find conflicts...")

    # Process all files to build the complete mapping
    groups = defaultdict(list)
    base_name_map = {}

    for filename in all_files:
        name_without_ext = filename.rsplit('.', 1)[0]
        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))
            lower_base_name = base_name.lower()

            groups[lower_base_name].append(number)

            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name

    # Find all groups that map to 'sakuracyan - [Ägir]'
    target_original = 'sakuracyan - [Ägir]'
    conflicting_groups = []

    for lower_base, original_base in base_name_map.items():
        if original_base == target_original:
            conflicting_groups.append((lower_base, original_base, groups[lower_base]))

    print(f"\nGroups that map to '{target_original}':")
    for lower_base, original_base, numbers in conflicting_groups:
        print(f"  '{lower_base}' -> '{original_base}': {len(numbers)} numbers: {sorted(numbers)}")

    # Also check for similar keys
    print("\nAll groups containing 'sakuracyan' and 'ägir':")
    for lower_base, numbers in groups.items():
        if "sakuracyan" in lower_base and "ägir" in lower_base:
            original_base = base_name_map[lower_base]
            print(f"  '{lower_base}' -> '{original_base}': {len(numbers)} numbers: {sorted(numbers)}")
    
    # Process them through the same logic as the main tool
    groups = defaultdict(list)
    base_name_map = {}
    
    print("\nProcessing files:")
    for filename in sakuracyan_agir_files:
        name_without_ext = filename.rsplit('.', 1)[0]
        match = re.match(r'^(.*)\s(\d+)$', name_without_ext)
        if match:
            base_name = match.group(1)
            number = int(match.group(2))
            lower_base_name = base_name.lower()
            
            print(f"  {filename} -> base='{base_name}' -> lower='{lower_base_name}' -> number={number}")
            
            groups[lower_base_name].append(number)
            
            if lower_base_name not in base_name_map:
                base_name_map[lower_base_name] = base_name
    
    print("\nGroups created:")
    for lower_base, numbers in groups.items():
        original_base = base_name_map[lower_base]
        print(f"  '{lower_base}' -> '{original_base}': {sorted(numbers)}")
    
    # Check for missing numbers
    print("\nChecking for missing numbers:")
    missing_by_group = {}
    
    for lower_base, numbers in groups.items():
        if len(numbers) <= 1:
            continue
        
        numbers.sort()
        original_base = base_name_map[lower_base]
        
        print(f"\nProcessing '{lower_base}' -> '{original_base}':")
        print(f"  Numbers: {numbers}")
        
        missing = []
        
        # Check for missing numbers before the start
        if 1 not in numbers and numbers[0] > 2:
            before_missing = list(range(2, numbers[0]))
            missing.extend(before_missing)
            print(f"  Missing before sequence: {before_missing}")
        else:
            print(f"  No missing before sequence (1 in numbers: {1 in numbers}, numbers[0]: {numbers[0]})")
        
        # Check for gaps
        for i in range(len(numbers) - 1):
            if numbers[i+1] - numbers[i] > 1:
                gap_missing = list(range(numbers[i] + 1, numbers[i+1]))
                missing.extend(gap_missing)
                print(f"  Gap found between {numbers[i]} and {numbers[i+1]}: {gap_missing}")
        
        print(f"  Final missing: {missing}")
        
        if missing:
            missing_by_group[original_base] = sorted(missing)
    
    print("\nFinal result:")
    for original_base, missing in missing_by_group.items():
        print(f"  '{original_base}': {missing}")
    
    return missing_by_group

if __name__ == "__main__":
    folder_path = "/Users/<USER>/Pictures/Anime/Cosplay"
    debug_sakuracyan_agir(folder_path)
