#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import unicodedata

# Test different representations of Ägir
test_strings = [
    "sakuracyan - [Ägir]",
    "guaxichan - [Ägir]",
    "yummychiyo - [Ägir (Patreon 2023-04)]"
]

for s in test_strings:
    print(f"String: {s}")
    print(f"  Lower: {s.lower()}")
    print(f"  Normalized NFC: {unicodedata.normalize('NFC', s.lower())}")
    print(f"  Normalized NFD: {unicodedata.normalize('NFD', s.lower())}")
    print(f"  Bytes: {s.encode('utf-8')}")
    print()

# Test if they're equal when lowercased
s1 = "sakuracyan - [Ägir]".lower()
s2 = "guaxichan - [Ägir]".lower()

print(f"Are they equal when lowercased? {s1 == s2}")
print(f"s1: '{s1}'")
print(f"s2: '{s2}'")
